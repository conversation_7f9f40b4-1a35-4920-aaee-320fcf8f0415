# model.py
import sqlite3
import pyodbc
import os
import shutil
from lxml import etree
import configparser
from pathlib import Path
import re

# --- <PERSON><PERSON><PERSON> v<PERSON> v<PERSON> l<PERSON> (gi<PERSON> nguyên) ---
class ConfigService:
    def __init__(self, config_file='config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser(interpolation=None)
        if not os.path.exists(self.config_file):
            self._create_default_config()
        self.config.read(self.config_file)
    def _create_default_config(self):
        self.config['Projects'] = {}
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    def get_projects(self):
        return list(self.config['Projects'].keys()) if 'Projects' in self.config else []
    def get_project_details(self, project_name):
        if 'Projects' not in self.config or project_name not in self.config['Projects']: return None
        project_path = self.config['Projects'][project_name]
        project_config_path = os.path.join(project_path, 'project.ini')
        if not os.path.exists(project_config_path): return None
        try:
            proj_config = configparser.ConfigParser(interpolation=None)
            proj_config.read(project_config_path)
            details = {
                'name': project_name,
                'description': proj_config.get('Details', 'Description', fallback=''),
                'source_path': proj_config.get('Paths', 'SourcePath', fallback=''),
                'sys_db_conn': proj_config.get('Database', 'SysDB', fallback=''),
                'app_db_conn': proj_config.get('Database', 'AppDB', fallback=''),
                'project_dir': project_path
            }
            return details
        except configparser.Error: return None
    def save_project(self, details):
        project_name = details['name']
        project_dir = os.path.join('projects', project_name)
        os.makedirs(project_dir, exist_ok=True)
        if 'Projects' not in self.config: self.config.add_section('Projects')
        self.config['Projects'][project_name] = project_dir
        with open(self.config_file, 'w') as f: self.config.write(f)
        proj_config = configparser.ConfigParser(interpolation=None)
        proj_config['Details'] = {'Description': details.get('description', '')}
        proj_config['Paths'] = {'SourcePath': details.get('source_path', '')}
        proj_config['Database'] = {'SysDB': details.get('sys_db_conn', ''), 'AppDB': details.get('app_db_conn', '')}
        with open(os.path.join(project_dir, 'project.ini'), 'w') as f: proj_config.write(f)
    def delete_project(self, project_name):
        if 'Projects' not in self.config or project_name not in self.config['Projects']: raise ValueError(f"Dự án '{project_name}' không tồn tại.")
        project_dir = self.config['Projects'][project_name]
        del self.config['Projects'][project_name]
        with open(self.config_file, 'w') as f: self.config.write(f)
        if os.path.isdir(project_dir): shutil.rmtree(project_dir)

# --- Dịch vụ Cơ sở dữ liệu (giữ nguyên) ---
class DatabaseService:
    def __init__(self): self.connection = None
    def _parse_conn_string(self, conn_str):
        params = {}
        for part in conn_str.split(';'):
            if '=' in part:
                key, value = part.split('=', 1)
                params[key.strip().lower()] = value.strip()
        return params
    def connect(self, connection_string):
        if not connection_string: return False, "Chuỗi kết nối trống."
        params = self._parse_conn_string(connection_string)
        server = params.get('data source') or params.get('server')
        if not server: return False, f"Chuỗi kết nối không chứa 'Data Source' hoặc 'SERVER'."
        database = params.get('initial catalog') or params.get('database')
        uid = params.get('user id') or params.get('uid')
        pwd = params.get('password') or params.get('pwd')
        clean_parts = [f"SERVER={server}"]
        if database: clean_parts.append(f"DATABASE={database}")
        if uid: clean_parts.append(f"UID={uid}")
        if pwd: clean_parts.append(f"PWD={pwd}")
        trusted = params.get('trusted_connection', 'no').lower()
        integrated_security = params.get('integrated security', '').lower()
        if trusted in ('yes', 'true') or integrated_security == 'sspi': clean_parts.append("Trusted_Connection=yes")
        base_conn_str = ";".join(clean_parts)
        common_drivers = ['ODBC Driver 17 for SQL Server', 'ODBC Driver 13 for SQL Server', 'SQL Server Native Client 11.0', 'SQL Server']
        last_error = None
        for driver in common_drivers:
            try:
                conn_str_with_driver = f'DRIVER={{{driver}}};{base_conn_str}'
                self.connection = pyodbc.connect(conn_str_with_driver, timeout=5)
                return True, self.connection
            except pyodbc.Error as e:
                last_error = e
                continue
        error_message = f"Không thể kết nối CSDL.\nĐã thử các driver: {', '.join(common_drivers)}.\nLỗi cuối cùng: {last_error}"
        self.connection = None
        return False, error_message
    def get_db_objects(self, conn):
        try:
            with conn.cursor() as cursor:
                tables = [row.TABLE_NAME for row in cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME").fetchall()]
                views = [row.TABLE_NAME for row in cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'VIEW' ORDER BY TABLE_NAME").fetchall()]
                sps = [row.ROUTINE_NAME for row in cursor.execute("SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE' ORDER BY ROUTINE_NAME").fetchall()]
                return {'Tables': tables, 'Views': views, 'Stored Procedures': sps}
        except Exception as e:
            print(f"Lỗi khi lấy đối tượng CSDL: {e}")
            return {}
    def get_table_details(self, conn, table_name):
        columns = []
        try:
            with conn.cursor() as cursor:
                for row in cursor.columns(table=table_name):
                    columns.append({'name': row.column_name, 'type': row.type_name, 'size': row.column_size, 'nullable': 'YES' if row.is_nullable == 'YES' else 'NO'})
        except Exception as e: print(f"Lỗi khi lấy chi tiết bảng {table_name}: {e}")
        return columns
    def get_object_definition(self, conn, object_name):
        try:
            with conn.cursor() as cursor:
                cursor.execute(f"sp_helptext '{object_name}'")
                return ''.join([row[0] for row in cursor.fetchall()])
        except Exception as e: return f"Không thể lấy định nghĩa cho {object_name}: {e}"

# --- Dịch vụ Phân tích File ---
class FileAnalyzer:
    """Phân tích các file mã nguồn (.f, .xml) để trích xuất thông tin."""
    def scan_web_config(self, source_path):
        web_config_path = Path(source_path) / 'web.config'
        if not web_config_path.exists(): return None, "Không tìm thấy file web.config"
        try:
            tree = etree.parse(str(web_config_path))
            conn_strings = tree.xpath('/configuration/connectionStrings/add')
            sys_db, app_dbs = '', []
            for conn in conn_strings:
                name = conn.get('name', '').lower()
                conn_str = conn.get('connectionString')
                if 'sys' in name: sys_db = conn_str
                else: app_dbs.append(conn_str)
            return sys_db, (app_dbs[0] if app_dbs else '')
        except Exception as e: return None, f"Lỗi khi đọc web.config: {e}"

    def _recursive_entity_resolver(self, content, base_dir, resolved_paths=None):
        """
        Đệ quy tìm và thay thế các entity dựa trên file trong một chuỗi XML.
        Đây là một bộ tiền xử lý trước khi đưa nội dung cho lxml.
        """
        if resolved_paths is None:
            resolved_paths = set()

        entity_pattern = re.compile(r'<!ENTITY\s+(%?)\s*([\w\.]+)\s+SYSTEM\s+"([^"]+)"\s*>')
        
        # Lặp lại tối đa 10 lần để xử lý các entity lồng nhau
        for _ in range(10):
            found_entities = entity_pattern.findall(content)
            if not found_entities:
                break 

            made_replacement = False
            for is_param, name, path in found_entities:
                entity_tag = f'%{name};' if is_param.strip() else f'&{name};'
                
                if entity_tag not in content:
                    continue

                full_path = os.path.abspath(os.path.join(base_dir, path))

                if full_path in resolved_paths:
                    continue 

                resolved_paths.add(full_path)
                
                entity_content = ""
                if os.path.exists(full_path):
                    try:
                        with open(full_path, 'r', encoding='utf-8') as f:
                            entity_content = self._recursive_entity_resolver(f.read(), os.path.dirname(full_path), resolved_paths)
                    except Exception as e:
                        entity_content = f"<!-- ERROR reading {path}: {e} -->"
                else:
                    entity_content = f"<!-- ENTITY NOT FOUND: {path} -->"
                
                content = content.replace(entity_tag, entity_content)
                made_replacement = True
            
            if not made_replacement:
                break

        return content

    def analyze_file(self, file_path):
        """
        (Nâng cấp) Tự xử lý các entity bằng cách thay thế văn bản trước khi parse.
        """
        original_content = ""
        clean_content = ""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            base_dir = os.path.dirname(file_path)

            # Bước 1: Đệ quy thay thế tất cả các entity bằng nội dung file của chúng
            resolved_content = self._recursive_entity_resolver(original_content, base_dir)

            # Bước 2: Xóa toàn bộ khối DOCTYPE và các phần không cần thiết
            # Xóa DOCTYPE declaration hoàn toàn
            doctype_pattern = re.compile(r'<!DOCTYPE[^>]*>(\s*\[.*?\])?>', re.DOTALL)
            clean_content = doctype_pattern.sub('', resolved_content)

            # Xóa các comment và CDATA sections có thể gây vấn đề
            clean_content = re.sub(r'<!--.*?-->', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<!\[IGNORE\[.*?\]\]>', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<!\[INCLUDE\[.*?\]\]>', '', clean_content, flags=re.DOTALL)

            # Xóa các entity declarations còn sót lại
            clean_content = re.sub(r'<!ENTITY[^>]*>', '', clean_content)

            # Dọn dẹp whitespace
            clean_content = clean_content.strip()

            # Bước 3: Parse chuỗi XML đã hoàn chỉnh
            parser = etree.XMLParser(recover=True, strip_cdata=False)
            root = etree.fromstring(clean_content.encode('utf-8'), parser=parser)

            # FIX: Kiểm tra xem việc parse có thành công không trước khi tiếp tục
            if root is None:
                raise etree.XMLSyntaxError("Không thể phân tích cấu trúc XML từ nội dung đã xử lý.", 0, 0, 0)

            fields_data = []
            for field_element in root.xpath('.//field | .//Field'):
                field_info = {
                    'Name': field_element.get('Name') or field_element.get('name'),
                    'DataType': field_element.get('DataType') or field_element.get('type'),
                    'Length': field_element.get('Length'),
                }
                if field_info['Name']:
                    fields_data.append(field_info)
            
            final_content_for_display = etree.tostring(root, pretty_print=True, encoding='unicode')

            return {
                "status": "success",
                "fields": fields_data,
                "content": final_content_for_display
            }
        except etree.XMLSyntaxError as e:
            # Sử dụng clean_content nếu có, nếu không thì dùng original_content
            content_to_show = clean_content if clean_content else original_content
            return { "status": "partial", "message": f"Lỗi phân tích XML sau khi xử lý entity: {e}. Hiển thị nội dung đã xử lý.", "content": content_to_show }
        except Exception as e:
            return {"status": "error", "message": str(e)}

# controller.py
import os
from concurrent.futures import ThreadPoolExecutor

class AppController:
    """Controller ch<PERSON><PERSON>, kế<PERSON> <PERSON><PERSON>i Model và View."""
    def __init__(self, model_classes, view):
        self.view = view
        self.config_service = model_classes['config']()
        self.db_service = model_classes['db']()
        self.file_analyzer = model_classes['analyzer']()
        
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.current_project_details = None
        
        self.full_file_tree = {}
        self.full_db_objects = {}
        
        self.initialize_app()

    def initialize_app(self):
        projects = self.config_service.get_projects()
        self.view.update_project_list(projects)

    def load_project(self, project_name):
        if not project_name:
            self.current_project_details = None
            self.view.clear_views()
            return

        self.current_project_details = self.config_service.get_project_details(project_name)
        if not self.current_project_details:
            self.view.show_error("Lỗi", f"<PERSON><PERSON><PERSON><PERSON> thể tải thông tin cho dự án '{project_name}'.")
            self.initialize_app()
            return
        
        self.view.clear_views()
        self.executor.submit(self._load_file_tree_async, self.current_project_details['source_path'])
        self.executor.submit(self._load_db_tree_async, self.current_project_details['sys_db_conn'])

    def _load_file_tree_async(self, source_path):
        if not os.path.isdir(source_path):
            self.view.after(0, lambda: self.view.show_error("Lỗi đường dẫn", f"Đường dẫn mã nguồn không tồn tại:\n{source_path}"))
            return
        tree_structure = {}
        for root, dirs, files in os.walk(source_path, topdown=True):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            relative_path = os.path.relpath(root, source_path)
            if relative_path == ".": relative_path = ""
            parent_node = tree_structure
            if relative_path:
                for part in relative_path.split(os.sep):
                    parent_node = parent_node.setdefault(part, {})
            for name in files:
                if name.endswith(('.f', '.xml', '.ent')):
                    parent_node[name] = os.path.join(root, name)
        self.full_file_tree = tree_structure
        self.view.after(0, lambda: self.view.update_file_tree(self.full_file_tree))

    def _load_db_tree_async(self, conn_str):
        is_connected, result = self.db_service.connect(conn_str)
        if not is_connected:
            self.view.after(0, lambda: self.view.show_error("Lỗi CSDL", result))
            return
        conn = result
        db_objects = self.db_service.get_db_objects(conn)
        conn.close()
        self.full_db_objects = db_objects
        self.view.after(0, lambda: self.view.update_db_tree(self.full_db_objects))

    def search_files(self, query):
        if not query:
            filtered_tree = self.full_file_tree
        else:
            filtered_tree = self._filter_file_tree_recursive(self.full_file_tree, query.lower())
        self.view.update_file_tree(filtered_tree, open_all=bool(query))

    def _filter_file_tree_recursive(self, node, query):
        filtered_node = {}
        for name, value in node.items():
            if isinstance(value, str):
                if query in name.lower():
                    filtered_node[name] = value
            elif isinstance(value, dict):
                if query in name.lower():
                    filtered_node[name] = value
                else:
                    sub_results = self._filter_file_tree_recursive(value, query)
                    if sub_results:
                        filtered_node[name] = sub_results
        return filtered_node

    def search_db_objects(self, query):
        if not query:
            filtered_objects = self.full_db_objects
        else:
            q_lower = query.lower()
            filtered_objects = {}
            for obj_type, obj_list in self.full_db_objects.items():
                matching_list = [obj for obj in obj_list if q_lower in obj.lower()]
                if matching_list:
                    filtered_objects[obj_type] = matching_list
        self.view.update_db_tree(filtered_objects)

    def analyze_file(self, file_path):
        if not file_path or not os.path.isfile(file_path): return
        self.executor.submit(self._analyze_file_async, file_path)

    def _analyze_file_async(self, file_path):
        """(Nâng cấp) Phân tích file và định dạng kết quả để hiển thị."""
        result = self.file_analyzer.analyze_file(file_path)
        
        display_text = ""
        if result['status'] == 'success':
            display_text += f"--- PHÂN TÍCH FILE: {os.path.basename(file_path)} ---\n\n"
            
            fields = result.get('fields', [])
            if fields:
                display_text += "--- CÁC TRƯỜNG DỮ LIỆU (FIELDS) ---\n"
                header = "{:<30} {:<20} {:<10}\n".format("Tên trường (Name)", "Kiểu (DataType)", "Độ dài (Length)")
                display_text += header
                display_text += "-" * 65 + "\n"
                for field in fields:
                    display_text += "{:<30} {:<20} {:<10}\n".format(
                        field.get('Name'), field.get('DataType'), field.get('Length')
                    )
                display_text += "\n"
            else:
                display_text += "--- Không tìm thấy trường dữ liệu (Field) nào. ---\n\n"

            # Bạn có thể thêm logic phân tích các cấu trúc khác (Form, Entity) ở đây
            
            display_text += "--- NỘI DUNG FILE GỐC ---\n"
            display_text += result.get('content', '')

        elif result['status'] == 'partial':
            display_text = f"--- {result.get('message')} ---\n\n"
            display_text += result.get('content', '')
        else: # status == 'error'
            display_text = f"Lỗi: {result.get('message', 'Lỗi không xác định')}"

        self.view.after(0, lambda: self.view.display_content(display_text))

    def show_db_object_details(self, object_type, object_name):
        self.executor.submit(self._get_db_object_details_async, object_type, object_name)

    def _get_db_object_details_async(self, object_type, object_name):
        if not self.current_project_details: return
        conn_str = self.current_project_details['sys_db_conn']
        is_connected, result = self.db_service.connect(conn_str)
        if not is_connected:
            self.view.after(0, lambda: self.view.show_error("Lỗi CSDL", result))
            return
        conn = result
        content = ""
        if object_type in ["Tables", "Views"]:
            columns = self.db_service.get_table_details(conn, object_name)
            content = f"--- Cấu trúc của {object_type[:-1]}: {object_name} ---\n\n"
            header = "{:<30} {:<20} {:<10} {:<10}\n".format("Tên cột", "Kiểu dữ liệu", "Kích thước", "Nullable")
            content += header + "-"*70 + "\n"
            for col in columns:
                content += "{:<30} {:<20} {:<10} {:<10}\n".format(str(col['name']), str(col['type']), str(col['size']), str(col['nullable']))
        elif object_type == "Stored Procedures":
            definition = self.db_service.get_object_definition(conn, object_name)
            content = f"--- Định nghĩa của SP: {object_name} ---\n\n{definition}"
        conn.close()
        self.view.after(0, lambda: self.view.display_content(content))

    def get_all_projects_details(self):
        project_names = self.config_service.get_projects()
        return [self.config_service.get_project_details(name) for name in project_names if self.config_service.get_project_details(name)]

    def delete_project(self, project_name):
        try:
            self.config_service.delete_project(project_name)
            self.view.show_info("Thành công", f"Đã xóa dự án '{project_name}'.")
            if self.current_project_details and self.current_project_details['name'] == project_name:
                self.current_project_details = None
                self.view.clear_views()
            self.initialize_app()
            return True
        except Exception as e:
            self.view.show_error("Lỗi", f"Không thể xóa dự án: {e}")
            return False

    def save_project(self, details):
        try:
            self.config_service.save_project(details)
            self.view.show_info("Thành công", f"Đã lưu dự án '{details['name']}' thành công.")
            self.initialize_app()
        except Exception as e:
            self.view.show_error("Lỗi", f"Không thể lưu dự án: {e}")

    def auto_fetch_db_info(self, source_path, callback):
        self.executor.submit(self._auto_fetch_db_info_async, source_path, callback)

    def _auto_fetch_db_info_async(self, source_path, callback):
        sys_db, app_db = self.file_analyzer.scan_web_config(source_path)
        self.view.after(0, lambda: callback(sys_db, app_db))

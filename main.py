# main.py
import model
import view
import controller

def main():
    """<PERSON>à<PERSON> chính để khởi chạy ứng dụng deAnalyst."""
    
    # Tập hợp các lớp Model để truyền cho Controller
    model_classes = {
        'config': model.ConfigService,
        'db': model.DatabaseService,
        'analyzer': model.FileAnalyzer
        # Lớp CacheService có thể được thêm vào sau khi hoàn thiện logic
    }
    
    # 1. Khởi tạo View
    app_view = view.MainAppView(title="deAnalyst - ERP Development Analyst Tool", theme="darkly")
    
    # 2. Khởi tạo Controller, truyền các lớp Model và instance của View vào
    app_controller = controller.AppController(model_classes, app_view)
    
    # 3. Gán Controller cho View để View có thể gọi các hàm của Controller
    app_view.set_controller(app_controller)
    
    # 4. Ch<PERSON>y vòng lặp chính của ứng dụng
    app_view.mainloop()

if __name__ == "__main__":
    # Để chạy ứng dụng, bạn cần cài đặt các thư viện:
    # pip install ttkbootstrap pyodbc lxml
    main()

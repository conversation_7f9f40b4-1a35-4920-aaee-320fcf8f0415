# view.py
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as bstrap
from ttkbootstrap.constants import *
import os

class MainAppView(bstrap.Window):
    """Lớp giao diện ch<PERSON>h của <PERSON>ng dụng."""
    def __init__(self, title, theme='superhero'):
        super().__init__(themename=theme)
        self.title(title)
        self.geometry("1200x800")
        self.controller = None
        self._create_widgets()
        self.update_idletasks()
        width, height = self.winfo_width(), self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def set_controller(self, controller):
        self.controller = controller

    def _create_widgets(self):
        self.menu_bar = tk.Menu(self)
        self.config(menu=self.menu_bar)
        
        project_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Dự án", menu=project_menu)
        project_menu.add_command(label="Dự án mới...", command=lambda: self.show_project_dialog())
        project_menu.add_command(label="Quản lý dự án...", command=self.show_project_manager)
        project_menu.add_separator()
        project_menu.add_command(label="Thoát", command=self.quit)

        main_pane = ttk.PanedWindow(self, orient=HORIZONTAL)
        main_pane.pack(fill=BOTH, expand=True, padx=5, pady=5)

        left_frame = ttk.Frame(main_pane, padding=5)
        
        proj_select_frame = ttk.Frame(left_frame)
        proj_select_frame.pack(fill=X, pady=(0, 5))
        ttk.Label(proj_select_frame, text="Dự án hiện tại:").pack(side=LEFT, padx=(0, 5))
        self.project_var = tk.StringVar()
        self.project_combo = ttk.Combobox(proj_select_frame, textvariable=self.project_var, state="readonly")
        self.project_combo.pack(side=LEFT, fill=X, expand=True)
        self.project_combo.bind("<<ComboboxSelected>>", self.on_project_selected)

        self.notebook = ttk.Notebook(left_frame)
        self.notebook.pack(fill=BOTH, expand=True)

        # --- Tab 1: Source Code ---
        tab1 = ttk.Frame(self.notebook)
        self.notebook.add(tab1, text="Mã nguồn")
        
        # Thêm ô tìm kiếm file
        self.file_search_var = tk.StringVar()
        file_search_entry = ttk.Entry(tab1, textvariable=self.file_search_var)
        file_search_entry.pack(fill=X, padx=2, pady=2)
        file_search_entry.bind("<KeyRelease>", self._on_search_files)

        self.file_tree = ttk.Treeview(tab1, show="tree headings")
        self.file_tree.heading("#0", text="Tên File / Thư mục")
        self.file_tree.pack(fill=BOTH, expand=True)
        self.file_tree.bind("<<TreeviewSelect>>", self.on_file_selected)

        # --- Tab 2: Database ---
        tab2 = ttk.Frame(self.notebook)
        self.notebook.add(tab2, text="Cơ sở dữ liệu")
        
        # Thêm ô tìm kiếm đối tượng CSDL
        self.db_search_var = tk.StringVar()
        db_search_entry = ttk.Entry(tab2, textvariable=self.db_search_var)
        db_search_entry.pack(fill=X, padx=2, pady=2)
        db_search_entry.bind("<KeyRelease>", self._on_search_db)

        self.db_tree = ttk.Treeview(tab2)
        self.db_tree.heading("#0", text="Đối tượng CSDL")
        self.db_tree.pack(fill=BOTH, expand=True)
        self.db_tree.bind("<<TreeviewSelect>>", self.on_db_object_selected)

        main_pane.add(left_frame, weight=1)

        right_frame = ttk.Frame(main_pane, padding=5)
        self.code_viewer = bstrap.ScrolledText(right_frame, wrap="none")
        self.code_viewer.pack(fill=BOTH, expand=True)
        self.code_viewer.config(state='disabled')
        main_pane.add(right_frame, weight=3)

    # --- Các hàm xử lý sự kiện ---
    def _on_search_files(self, event=None):
        if self.controller:
            query = self.file_search_var.get()
            self.controller.search_files(query)

    def _on_search_db(self, event=None):
        if self.controller:
            query = self.db_search_var.get()
            self.controller.search_db_objects(query)

    def on_project_selected(self, event=None):
        # Xóa các ô tìm kiếm khi đổi dự án
        self.file_search_var.set("")
        self.db_search_var.set("")
        if self.controller:
            self.controller.load_project(self.project_var.get())

    def on_file_selected(self, event=None):
        if self.controller:
            selected_item = self.file_tree.focus()
            if selected_item:
                file_path = self.file_tree.item(selected_item, "values")
                if file_path and isinstance(file_path, (list, tuple)) and file_path[0]:
                    self.controller.analyze_file(file_path[0])

    def on_db_object_selected(self, event=None):
        if self.controller:
            selected_item = self.db_tree.focus()
            if selected_item and self.db_tree.parent(selected_item):
                parent_id = self.db_tree.parent(selected_item)
                object_type = self.db_tree.item(parent_id, "text")
                object_name = self.db_tree.item(selected_item, "text")
                self.controller.show_db_object_details(object_type, object_name)

    # --- Các hàm cập nhật giao diện ---
    def update_project_list(self, projects):
        current_project = self.project_var.get()
        self.project_combo['values'] = projects
        if current_project in projects:
            self.project_var.set(current_project)
        elif projects:
            self.project_var.set(projects[0])
        else:
            self.project_var.set('')
        self.on_project_selected()

    def update_file_tree(self, tree_structure, open_all=False):
        self.clear_file_tree()
        self._populate_file_tree(self.file_tree, "", tree_structure, open_all)

    def _populate_file_tree(self, tree, parent_id, node, open_all):
        for name, value in sorted(node.items()):
            if isinstance(value, dict):
                folder_id = tree.insert(parent_id, "end", text=name, values=('',), open=open_all)
                self._populate_file_tree(tree, folder_id, value, open_all)
            else:
                tree.insert(parent_id, "end", text=name, values=(value,))

    def update_db_tree(self, db_objects):
        self.clear_db_tree()
        for obj_type, obj_list in db_objects.items():
            parent_id = self.db_tree.insert("", "end", text=obj_type, open=True)
            for obj_name in sorted(obj_list):
                self.db_tree.insert(parent_id, "end", text=obj_name)
    
    def display_content(self, content):
        self.code_viewer.config(state='normal')
        self.code_viewer.delete('1.0', END)
        self.code_viewer.insert('1.0', content)
        self.code_viewer.config(state='disabled')

    def clear_views(self):
        self.clear_file_tree()
        self.clear_db_tree()
        self.display_content("")

    def clear_file_tree(self):
        for i in self.file_tree.get_children(): self.file_tree.delete(i)
    def clear_db_tree(self):
        for i in self.db_tree.get_children(): self.db_tree.delete(i)

    def show_project_dialog(self, project_details=None):
        ProjectDialog(self, self.controller, project_details)

    def show_project_manager(self):
        if self.controller:
            ProjectManagerDialog(self, self.controller)

    def show_info(self, title, message): messagebox.showinfo(title, message)
    def show_error(self, title, message): messagebox.showerror(title, message)

# --- Các lớp Dialog (giữ nguyên) ---
class ProjectManagerDialog(bstrap.Toplevel):
    def __init__(self, parent, controller):
        super().__init__(parent)
        self.controller = controller
        self.transient(parent)
        self.grab_set()
        self.title("Quản lý dự án")
        self.geometry("600x400")
        self._create_widgets()
        self._load_projects()
        self._center_window()

    def _center_window(self):
        self.withdraw()
        self.update_idletasks()
        parent = self.master
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f'+{x}+{y}')
        self.deiconify()

    def _create_widgets(self):
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        cols = ("name", "path")
        self.tree = ttk.Treeview(main_frame, columns=cols, show="headings")
        self.tree.heading("name", text="Tên dự án")
        self.tree.heading("path", text="Đường dẫn mã nguồn")
        self.tree.column("name", width=150)
        self.tree.pack(side=TOP, fill=BOTH, expand=True)
        btn_frame = ttk.Frame(main_frame, padding=(0, 10, 0, 0))
        btn_frame.pack(side=BOTTOM, fill=X)
        ttk.Button(btn_frame, text="Đóng", command=self.destroy).pack(side=RIGHT, padx=5)
        ttk.Button(btn_frame, text="Xóa", command=self._on_delete, style=DANGER).pack(side=RIGHT, padx=5)
        ttk.Button(btn_frame, text="Sửa", command=self._on_edit, style=SUCCESS).pack(side=RIGHT, padx=5)

    def _load_projects(self):
        for i in self.tree.get_children(): self.tree.delete(i)
        projects = self.controller.get_all_projects_details()
        for proj in projects:
            self.tree.insert("", "end", values=(proj['name'], proj['source_path']), iid=proj['name'])

    def _get_selected_project_name(self):
        selected_item = self.tree.focus()
        if not selected_item:
            messagebox.showwarning("Chưa chọn dự án", "Vui lòng chọn một dự án.", parent=self)
            return None
        return self.tree.item(selected_item, "values")[0]

    def _on_edit(self):
        project_name = self._get_selected_project_name()
        if project_name:
            details = self.controller.config_service.get_project_details(project_name)
            if details:
                self.master.show_project_dialog(project_details=details)
                self.destroy()

    def _on_delete(self):
        project_name = self._get_selected_project_name()
        if project_name:
            if messagebox.askyesno("Xác nhận xóa", f"Bạn có chắc chắn muốn xóa dự án '{project_name}' không?", parent=self):
                if self.controller.delete_project(project_name):
                    self._load_projects()

class ProjectDialog(bstrap.Toplevel):
    def __init__(self, parent, controller, project_details=None):
        super().__init__(parent)
        self.transient(parent)
        self.grab_set()
        self.controller = controller
        title = "Chỉnh sửa dự án" if project_details else "Tạo dự án mới"
        self.title(title)
        self._create_form()
        if project_details:
            self._fill_form(project_details)
            self.entries['name'].config(state='readonly')
        self._center_window()

    def _center_window(self):
        self.withdraw()
        self.update_idletasks()
        parent = self.master
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f'+{x}+{y}')
        self.deiconify()
        
    def _create_form(self):
        form_frame = ttk.Frame(self, padding=20)
        form_frame.pack(fill=BOTH, expand=True)
        fields = {
            "name": "Tên dự án:", "description": "Mô tả:",
            "source_path": "Đường dẫn mã nguồn:",
            "sys_db_conn": "Chuỗi kết nối Sys DB:", "app_db_conn": "Chuỗi kết nối App DB:"
        }
        self.entries = {}
        for i, (key, label) in enumerate(fields.items()):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky=W, pady=2, padx=5)
            if "path" in key:
                path_frame = ttk.Frame(form_frame)
                path_frame.grid(row=i, column=1, sticky=EW, pady=2)
                self.entries[key] = ttk.Entry(path_frame)
                self.entries[key].pack(side=LEFT, fill=X, expand=True)
                ttk.Button(path_frame, text="...", width=3, command=lambda k=key: self._browse_folder(k)).pack(side=LEFT, padx=(5,0))
            else:
                self.entries[key] = ttk.Entry(form_frame)
                self.entries[key].grid(row=i, column=1, sticky=EW, pady=2)
        form_frame.columnconfigure(1, weight=1)
        auto_btn = ttk.Button(form_frame, text="Lấy thông tin tự động từ web.config", command=self.auto_get_info)
        auto_btn.grid(row=len(fields), column=1, sticky=E, pady=10)
        btn_frame = ttk.Frame(form_frame)
        btn_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=(10,0))
        ttk.Button(btn_frame, text="Lưu", command=self.save, style=SUCCESS).pack(side=LEFT, padx=5)
        ttk.Button(btn_frame, text="Hủy", command=self.destroy, style=DANGER).pack(side=LEFT, padx=5)

    def _fill_form(self, project_details):
        for key, widget in self.entries.items():
            widget.insert(0, project_details.get(key, ''))

    def _browse_folder(self, key):
        folder = filedialog.askdirectory(parent=self)
        if folder:
            self.entries[key].delete(0, END)
            self.entries[key].insert(0, folder)

    def auto_get_info(self):
        source_path = self.entries['source_path'].get()
        if not source_path:
            messagebox.showwarning("Thiếu thông tin", "Vui lòng cung cấp đường dẫn mã nguồn trước.", parent=self)
            return
        if self.controller:
            self.controller.auto_fetch_db_info(source_path, self.update_db_connections)

    def update_db_connections(self, sys_db, app_db):
        if sys_db is not None or app_db is not None:
            self.entries['sys_db_conn'].delete(0, END)
            self.entries['sys_db_conn'].insert(0, sys_db or '')
            self.entries['app_db_conn'].delete(0, END)
            self.entries['app_db_conn'].insert(0, app_db or '')
            messagebox.showinfo("Thành công", "Đã tìm thấy và điền thông tin kết nối CSDL.", parent=self)
        else:
            messagebox.showerror("Lỗi", app_db, parent=self)

    def save(self):
        details = {key: widget.get() for key, widget in self.entries.items()}
        if not details['name'] or not details['source_path']:
            messagebox.showwarning("Thiếu thông tin", "Tên dự án và đường dẫn mã nguồn là bắt buộc.", parent=self)
            return
        if self.controller:
            self.controller.save_project(details)
        self.destroy()
